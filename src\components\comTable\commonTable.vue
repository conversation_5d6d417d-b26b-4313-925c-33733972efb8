<!-- 普通表格 -->
<template>
  <div class="table-main">
    <el-table
      ref="table"
      v-bind="$attrs"
      :data="tableData"
      :height="tableHeight"
      :max-height="maxHeight"
      :header-cell-style="cellStyle"
      @row-click="rowClick"
      :border="border"
      :row-key="rowKey"
      @selection-change="handleSelectionChange"
    >
      <!-- 序号列 -->
      <el-table-column
        v-if="showIndexColumn"
        type="index"
        label="序号"
        width="60"
        align="center"
      />

      <!-- 动态渲染列 -->
      <template v-for="(item, index) in colums">
        <!-- 嵌套列 -->
        <el-table-column
          v-if="item.children && item.children.length"
          :key="'nested-' + index"
          v-bind="item"
          :align="item.align || 'center'"
          :width="item.width || 'auto'"
        >
          <el-table-column
            v-for="(child, cIndex) in item.children"
            :key="'child-' + cIndex"
            v-bind="child"
            :align="child.align || 'center'"
            :width="child.width || 'auto'"
          >
            <template v-slot="scope">
              <slot
                v-if="child.slotName"
                :name="child.slotName"
                :scope="scope"
              />
              <span
                v-else
                :style="{ color: getTextColor(child, scope.row, scope.$index) }"
              >
                {{ scope.row[child.prop] }}
              </span>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 普通列 -->
        <el-table-column
          v-else
          :key="'normal-' + index"
          v-bind="item"
          :align="item.align || 'center'"
          :width="item.width || 'auto'"
        >
          <template v-slot="scope">
            <!-- 操作列 -->
            <slot
              v-if="item.prop === 'operation'"
              name="operation"
              :scope="scope"
            />
            <!-- 插槽列 -->
            <slot
              v-else-if="item.slotName"
              :name="item.slotName"
              :scope="scope"
            />
            <!-- 普通文本列 -->
            <span
              v-else
              :style="{ color: getTextColor(item, scope.row, scope.$index) }"
            >
              {{ scope.row[item.prop] }}
            </span>
          </template>
        </el-table-column>
      </template>

      <!-- 空数据提示 -->
      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-if="showPagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :layout="paginationLayout"
        :total="total"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "CommonTable",
  props: {
    colums: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    tableHeight: {
      type: [String, Number],
      default: "100%",
    },
    maxHeight: {
      type: [String, Number],
      default: "100%",
    },
    border: {
      type: Boolean,
      default: true,
    },
    rowKey: {
      type: String,
      default: "",
    },
    cellStyle: {
      type: Object,
      default: () => {},
      required: false,
    },
    showIndexColumn: {
      type: Boolean,
      default: true,
    },
    // 分页相关props
    showPagination: {
      type: Boolean,
      default: true,
    },
    total: {
      type: Number,
      default: 0,
    },
    pageSize: {
      type: Number,
      default: 6,
    },
    currentPage: {
      type: Number,
      default: 1,
    },
    pageSizes: {
      type: Array,
      default: () => [5,10, 20, 30, 50],
    },
    paginationLayout: {
      type: String,
      default: "total, sizes, prev, pager, next, jumper",
    },
  },
  methods: {
    rowClick(row) {
      this.$emit("rowClick", row);
    },
    handleSelectionChange(val) {
      this.$emit("handleSelectionChange", val);
    },
    getTextColor(column, row, index) {
      const color = column.textColor;
      if (typeof color === "function") {
        return color(row, index) || "#fff";
      }
      return color || "#fff";
    },
    // 分页方法
    handleSizeChange(val) {
      this.$emit("page-size-change", val);
    },
    handleCurrentChange(val) {
      this.$emit("current-page-change", val);
    },
  },
};
</script>

<style lang="scss" scoped>
.table-main {
  height: 100%;
  display: flex;
  flex-direction: column;
  .el-table {
    flex: 1;
  }

  .pagination-container {
    margin-top: 16px;
    text-align: right;
  }
}

::v-deep .el-table,
.el-table__expanded-cell {
  background-color: #1b2242 !important;
}
::v-deep .el-table::before {
  height: 0px;
}
::v-deep .el-table tr {
  background-color: #1b2242 !important;
  font-family: Source Han Sans;
  font-size: 14px;
  font-weight: normal;
  letter-spacing: normal;
  color: #ffffff;
}

/* 斑马纹效果 - 奇数行（浅色行）使用 #1A2E5E */
::v-deep .el-table__body tr:nth-child(odd) {
  background-color: #1A2E5E !important;
}

/* 斑马纹效果 - 偶数行（深色行）保持原色 #1b2242 */
::v-deep .el-table__body tr:nth-child(even) {
  background-color: #1b2242 !important;
}

::v-deep .el-table td.el-table__cell {
  border: none;
}

::v-deep .el-table__header-wrapper {
  border-radius: 8px;
  background: #162549;
  border-width: 0px 0px 2px 0px;
  border-style: solid;
  border-color: #1783ff;

  font-family: Source Han Sans;
  font-size: 16px;
  font-weight: 500;
}

::v-deep
  .el-table--enable-row-hover
  .el-table__body
  tr:hover
  > td.el-table__cell {
  background: #254489 !important;
  color: #fff !important;
}

/* 确保悬停效果在斑马纹基础上正常工作 */
::v-deep .el-table__body tr:nth-child(odd):hover > td.el-table__cell {
  background: #254489 !important;
  color: #fff !important;
}

::v-deep .el-table__body tr:nth-child(even):hover > td.el-table__cell {
  background: #254489 !important;
  color: #fff !important;
}

::v-deep .el-table__fixed-right::before {
  height: 0;
}

::v-deep .el-table__body tr.hover-row > td.el-table__cell {
  background: #254489 !important;
}

/* 普通页码按钮 */
::v-deep .el-pagination .el-pager li {
  border-radius: 4px;
  background: rgba(17, 53, 117, 0.6) !important;
  margin-right: 8px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85) !important;
}

/* 当前页码按钮 */
::v-deep .el-pagination .el-pager li.active {
  // background-color: #409eff;
  background: rgba(17, 53, 117, 0.6);
  border: 1px solid #33fefe;
  color: #fff;
}

/* 鼠标悬浮页码 */
// ::v-deep .el-pagination.is-background .el-pager li:hover {
//   // background-color: #c6e2ff;
// }

/* 上一页按钮 */
::v-deep .el-pagination .btn-prev {
  border-radius: 4px;
  background: rgba(17, 53, 117, 0.6) !important;
  font-size: 14px;
  color: #ffffff;
  margin-right: 8px;
}

/* 下一页按钮 */
::v-deep .el-pagination .btn-next {
  border-radius: 4px;
  background: rgba(17, 53, 117, 0.6) !important;
  font-size: 14px;
  color: #ffffff;
  margin-right: 8px;
}

/* 悬停效果 */
::v-deep .el-pagination .btn-prev:hover,
.el-pagination .btn-next:hover {
  background-color: #c6e2ff;
}

::v-deep .el-pagination__sizes input {
  border: none !important;
}

::v-deep .el-pagination__jump {
  margin-left: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
  input {
    border: none !important;
  }
}
</style>
